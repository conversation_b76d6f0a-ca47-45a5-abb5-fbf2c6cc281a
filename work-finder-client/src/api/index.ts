// API call functions and configurations
export { authApi } from "../features/auth/api";
export { companiesApi } from "../features/companies/api";
export { jobsApi } from "../features/jobs/api";

// API types
export type {
  User,
  LoginCredentials,
  SignupData,
  AuthResponse,
} from "../features/auth/api";

export type { Company, CompanyFilters } from "../features/companies/types";

export type { PaginatedCompaniesResponse } from "../features/companies/api";

export type {
  JobsResponse as PaginatedJobsResponse,
  JobSearchParams,
} from "../features/jobs/api";

export type { Job, JobFilters, JobApplication } from "../features/jobs/types";

// Common API utilities
export const API_BASE_URL =
  process.env.VITE_API_BASE_URL || "http://localhost:3001/api";

export const createApiHeaders = (token?: string) => ({
  "Content-Type": "application/json",
  ...(token && { Authorization: `Bearer ${token}` }),
});

export const handleApiError = (error: any) => {
  if (error.response) {
    // Server responded with error status
    return error.response.data?.message || "Server error occurred";
  } else if (error.request) {
    // Request was made but no response received
    return "Network error - please check your connection";
  } else {
    // Something else happened
    return error.message || "An unexpected error occurred";
  }
};
