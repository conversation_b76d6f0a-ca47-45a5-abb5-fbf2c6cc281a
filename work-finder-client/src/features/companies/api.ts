import { apiClient } from '@/api/client'
import { API_ENDPOINTS } from '@/api/apiEndpoints'
import { Company, CompanyFilters, CreateCompanyData, UpdateCompanyData, CompanyProfile } from './types'

export interface PaginatedCompaniesResponse {
  companies: Company[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface CompanySearchParams extends CompanyFilters {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

export const companiesApi = {
  // Get all companies with filtering and pagination
  async getCompanies(params?: CompanySearchParams): Promise<PaginatedCompaniesResponse> {
    const response = await apiClient.get(API_ENDPOINTS.COMPANIES.LIST, { params })
    return response.data
  },

  // Get a specific company by ID
  async getCompany(id: string): Promise<CompanyProfile> {
    const response = await apiClient.get(API_ENDPOINTS.COMPANIES.DETAIL(id))
    return response.data
  },

  // Create a new company
  async createCompany(data: CreateCompanyData): Promise<Company> {
    const response = await apiClient.post(API_ENDPOINTS.COMPANIES.LIST, data)
    return response.data
  },

  // Update an existing company
  async updateCompany(data: UpdateCompanyData): Promise<Company> {
    const { id, ...updateData } = data
    const response = await apiClient.patch(API_ENDPOINTS.COMPANIES.DETAIL(id), updateData)
    return response.data
  },

  // Delete a company
  async deleteCompany(id: string): Promise<void> {
    await apiClient.delete(API_ENDPOINTS.COMPANIES.DETAIL(id))
  },

  // Follow a company
  async followCompany(companyId: string): Promise<void> {
    await apiClient.post(API_ENDPOINTS.COMPANIES.FOLLOW(companyId))
  },

  // Unfollow a company
  async unfollowCompany(companyId: string): Promise<void> {
    await apiClient.delete(API_ENDPOINTS.COMPANIES.UNFOLLOW(companyId))
  },

  // Get followed companies
  async getFollowedCompanies(): Promise<Company[]> {
    const response = await apiClient.get(API_ENDPOINTS.COMPANIES.FOLLOWED)
    return response.data
  },

  // Search companies
  async searchCompanies(query: string, filters?: CompanyFilters): Promise<PaginatedCompaniesResponse> {
    const params = { search: query, ...filters }
    const response = await apiClient.get(API_ENDPOINTS.COMPANIES.LIST, { params })
    return response.data
  }
}
