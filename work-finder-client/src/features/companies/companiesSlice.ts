import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { Company, CompanyFilters, CompaniesState } from './types'
import { PaginatedCompaniesResponse } from './api'

const initialState: CompaniesState = {
  companies: [],
  loading: false,
  error: null,
  filters: {},
  currentCompany: null,
  followedCompanies: []
}

const companiesSlice = createSlice({
  name: 'companies',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    setCompanies: (state, action: PayloadAction<PaginatedCompaniesResponse>) => {
      state.companies = action.payload.companies
      state.error = null
    },
    addCompany: (state, action: PayloadAction<Company>) => {
      state.companies.unshift(action.payload)
    },
    updateCompany: (state, action: PayloadAction<Company>) => {
      const index = state.companies.findIndex(company => company.id === action.payload.id)
      if (index !== -1) {
        state.companies[index] = action.payload
      }
    },
    deleteCompany: (state, action: PayloadAction<string>) => {
      state.companies = state.companies.filter(company => company.id !== action.payload)
    },
    setCurrentCompany: (state, action: PayloadAction<Company | null>) => {
      state.currentCompany = action.payload
    },
    setFilters: (state, action: PayloadAction<CompanyFilters>) => {
      state.filters = action.payload
    },
    clearFilters: (state) => {
      state.filters = {}
    },
    setFollowedCompanies: (state, action: PayloadAction<Company[]>) => {
      state.followedCompanies = action.payload
    },
    followCompany: (state, action: PayloadAction<string>) => {
      const company = state.companies.find(c => c.id === action.payload)
      if (company) {
        company.isFollowing = true
        company.followersCount = (company.followersCount || 0) + 1
      }
    },
    unfollowCompany: (state, action: PayloadAction<string>) => {
      const company = state.companies.find(c => c.id === action.payload)
      if (company) {
        company.isFollowing = false
        company.followersCount = Math.max((company.followersCount || 1) - 1, 0)
      }
      state.followedCompanies = state.followedCompanies.filter(c => c.id !== action.payload)
    },
    clearError: (state) => {
      state.error = null
    },
    clearCompanies: (state) => {
      state.companies = []
    }
  }
})

export const {
  setLoading,
  setError,
  setCompanies,
  addCompany,
  updateCompany,
  deleteCompany,
  setCurrentCompany,
  setFilters,
  clearFilters,
  setFollowedCompanies,
  followCompany,
  unfollowCompany,
  clearError,
  clearCompanies
} = companiesSlice.actions

export default companiesSlice.reducer
