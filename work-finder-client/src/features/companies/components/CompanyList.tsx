import { Company } from '../types'

interface CompanyListProps {
  companies: Company[]
  loading: boolean
  onFollow?: (companyId: string) => void
  onUnfollow?: (companyId: string) => void
  onDelete?: (companyId: string) => void
}

export function CompanyList({ companies, loading, onFollow, onUnfollow, onDelete }: CompanyListProps) {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (companies.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No companies found</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {companies.map((company) => (
        <div key={company.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              {company.logo && (
                <img 
                  src={company.logo} 
                  alt={company.name}
                  className="w-12 h-12 rounded-lg object-cover mr-3"
                />
              )}
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{company.name}</h3>
                <p className="text-sm text-gray-600">{company.industry}</p>
              </div>
            </div>
          </div>

          <p className="text-gray-700 text-sm mb-4 line-clamp-3">{company.description}</p>

          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            <span>{company.location}</span>
            <span>{company.jobsCount} jobs</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              {company.isFollowing ? (
                <button
                  onClick={() => onUnfollow?.(company.id)}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                >
                  Following
                </button>
              ) : (
                <button
                  onClick={() => onFollow?.(company.id)}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Follow
                </button>
              )}
            </div>

            {onDelete && (
              <button
                onClick={() => onDelete(company.id)}
                className="text-red-600 hover:text-red-800 text-sm"
              >
                Delete
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
