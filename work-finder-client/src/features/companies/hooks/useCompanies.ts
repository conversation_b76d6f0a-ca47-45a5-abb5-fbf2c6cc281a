import { useState, useCallback, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store'
import { companiesApi } from '../api'
import { Company, CompanyFilters, CreateCompanyData } from '../types'
import { 
  setCompanies, 
  setLoading, 
  setError, 
  setFilters,
  addCompany,
  updateCompany,
  deleteCompany,
  followCompany,
  unfollowCompany,
  setFollowedCompanies
} from '../companiesSlice'

export function useCompanies() {
  const dispatch = useDispatch()
  const { companies, loading, error, filters, followedCompanies } = useSelector((state: RootState) => state.companies)

  const fetchCompanies = useCallback(async (searchFilters?: CompanyFilters) => {
    try {
      dispatch(setLoading(true))
      dispatch(setError(null))
      
      const response = await companiesApi.getCompanies(searchFilters)
      dispatch(setCompanies(response))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch companies'
      dispatch(setError(errorMessage))
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const createCompany = useCallback(async (companyData: CreateCompanyData) => {
    try {
      dispatch(setLoading(true))
      dispatch(setError(null))
      
      const newCompany = await companiesApi.createCompany(companyData)
      dispatch(addCompany(newCompany))
      return { success: true, company: newCompany }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create company'
      dispatch(setError(errorMessage))
      return { success: false, error: errorMessage }
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const updateCompanyById = useCallback(async (id: string, companyData: Partial<CreateCompanyData>) => {
    try {
      dispatch(setLoading(true))
      dispatch(setError(null))
      
      const updatedCompany = await companiesApi.updateCompany({ id, ...companyData })
      dispatch(updateCompany(updatedCompany))
      return { success: true, company: updatedCompany }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update company'
      dispatch(setError(errorMessage))
      return { success: false, error: errorMessage }
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const deleteCompanyById = useCallback(async (id: string) => {
    try {
      dispatch(setLoading(true))
      dispatch(setError(null))
      
      await companiesApi.deleteCompany(id)
      dispatch(deleteCompany(id))
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete company'
      dispatch(setError(errorMessage))
      return { success: false, error: errorMessage }
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const followCompanyById = useCallback(async (companyId: string) => {
    try {
      await companiesApi.followCompany(companyId)
      dispatch(followCompany(companyId))
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to follow company'
      dispatch(setError(errorMessage))
      return { success: false, error: errorMessage }
    }
  }, [dispatch])

  const unfollowCompanyById = useCallback(async (companyId: string) => {
    try {
      await companiesApi.unfollowCompany(companyId)
      dispatch(unfollowCompany(companyId))
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unfollow company'
      dispatch(setError(errorMessage))
      return { success: false, error: errorMessage }
    }
  }, [dispatch])

  const fetchFollowedCompanies = useCallback(async () => {
    try {
      dispatch(setLoading(true))
      const followedCompanies = await companiesApi.getFollowedCompanies()
      dispatch(setFollowedCompanies(followedCompanies))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch followed companies'
      dispatch(setError(errorMessage))
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const applyFilters = useCallback((newFilters: CompanyFilters) => {
    dispatch(setFilters(newFilters))
    fetchCompanies(newFilters)
  }, [dispatch, fetchCompanies])

  // Auto-fetch companies on mount
  useEffect(() => {
    if (companies.length === 0) {
      fetchCompanies(filters)
    }
  }, [])

  return {
    companies,
    loading,
    error,
    filters,
    followedCompanies,
    fetchCompanies,
    createCompany,
    updateCompany: updateCompanyById,
    deleteCompany: deleteCompanyById,
    followCompany: followCompanyById,
    unfollowCompany: unfollowCompanyById,
    fetchFollowedCompanies,
    applyFilters
  }
}
