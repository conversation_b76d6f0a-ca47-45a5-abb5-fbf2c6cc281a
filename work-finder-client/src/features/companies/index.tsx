import { CompanyList } from './components/CompanyList'
import { CompanyForm } from './components/CompanyForm'
import { CompanyFilter } from './components/CompanyFilter'
import { useCompanies } from './hooks/useCompanies'

export interface CompaniesFeatureProps {
  mode: 'list' | 'create' | 'edit'
  companyId?: string
  onSuccess?: () => void
}

export function CompaniesFeature({ mode, companyId, onSuccess }: CompaniesFeatureProps) {
  const { 
    companies, 
    loading, 
    error, 
    fetchCompanies, 
    createCompany, 
    updateCompany, 
    deleteCompany,
    followCompany,
    unfollowCompany
  } = useCompanies()

  const handleCreateCompany = async (companyData: any) => {
    const result = await createCompany(companyData)
    if (result.success && onSuccess) {
      onSuccess()
    }
  }

  const handleUpdateCompany = async (companyData: any) => {
    if (!companyId) return
    const result = await updateCompany(companyId, companyData)
    if (result.success && onSuccess) {
      onSuccess()
    }
  }

  const handleDeleteCompany = async (id: string) => {
    const result = await deleteCompany(id)
    if (result.success && onSuccess) {
      onSuccess()
    }
  }

  const handleFollowCompany = async (id: string) => {
    await followCompany(id)
  }

  const handleUnfollowCompany = async (id: string) => {
    await unfollowCompany(id)
  }

  if (mode === 'create') {
    return (
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Create New Company</h1>
        <CompanyForm onSubmit={handleCreateCompany} loading={loading} />
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
      </div>
    )
  }

  if (mode === 'edit' && companyId) {
    return (
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Edit Company</h1>
        <CompanyForm 
          companyId={companyId}
          onSubmit={handleUpdateCompany} 
          loading={loading} 
        />
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Companies</h1>
        <CompanyFilter onFilter={fetchCompanies} />
      </div>
      
      <CompanyList 
        companies={companies}
        loading={loading}
        onFollow={handleFollowCompany}
        onUnfollow={handleUnfollowCompany}
        onDelete={handleDeleteCompany}
      />
      
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}
    </div>
  )
}

// Export individual components for flexibility
export { CompanyList, CompanyForm, CompanyFilter }
export { useCompanies }
export * from './types'
