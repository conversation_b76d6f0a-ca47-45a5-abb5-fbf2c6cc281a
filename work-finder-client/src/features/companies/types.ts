export interface Company {
  id: string
  name: string
  description: string
  logo?: string
  website?: string
  industry: string
  location?: string
  size?: string
  founded?: string
  jobsCount?: number
  followersCount?: number
  isFollowing?: boolean
  createdAt?: string
  updatedAt?: string
}

export interface CreateCompanyData {
  name: string
  description: string
  logo?: string
  website?: string
  industry: string
  location?: string
  size?: string
  founded?: string
}

export interface UpdateCompanyData extends Partial<CreateCompanyData> {
  id: string
}

export interface CompanyFilters {
  search?: string
  industry?: string
  location?: string
  size?: string
}

export interface CompaniesState {
  companies: Company[]
  loading: boolean
  error: string | null
  filters: CompanyFilters
  currentCompany: Company | null
  followedCompanies: Company[]
}

export interface CompanyJob {
  id: string
  title: string
  location: string
  type: string
  postedAt: string
  applicationsCount?: number
}

export interface CompanyProfile extends Company {
  jobs: CompanyJob[]
  totalJobs: number
}
