import { apiClient } from '@/api/client'
import { API_ENDPOINTS } from '@/api/apiEndpoints'
import { Job, JobFilters, CreateJobData, UpdateJobData } from './types'

export interface JobsResponse {
  jobs: Job[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface JobApplication {
  id: string
  jobId: string
  userId: string
  status: 'pending' | 'accepted' | 'rejected'
  appliedAt: string
  coverLetter?: string
  resumeUrl?: string
}

export interface JobSearchParams extends JobFilters {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

export const jobsApi = {
  // Get all jobs with filtering and pagination
  async getJobs(params?: JobSearchParams): Promise<JobsResponse> {
    const response = await apiClient.get(API_ENDPOINTS.JOBS.LIST, { params })
    return response.data
  },

  // Get a specific job by ID
  async getJob(id: string): Promise<Job> {
    const response = await apiClient.get(API_ENDPOINTS.JOBS.DETAIL(id))
    return response.data
  },

  // Create a new job
  async createJob(data: CreateJobData): Promise<Job> {
    const response = await apiClient.post(API_ENDPOINTS.JOBS.LIST, data)
    return response.data
  },

  // Update an existing job
  async updateJob(data: UpdateJobData): Promise<Job> {
    const { id, ...updateData } = data
    const response = await apiClient.patch(API_ENDPOINTS.JOBS.DETAIL(id), updateData)
    return response.data
  },

  // Delete a job
  async deleteJob(id: string): Promise<void> {
    await apiClient.delete(API_ENDPOINTS.JOBS.DETAIL(id))
  },

  // Apply to a job
  async applyToJob(jobId: string, applicationData: { coverLetter?: string; resumeId?: string }): Promise<JobApplication> {
    const response = await apiClient.post(API_ENDPOINTS.JOBS.APPLY(jobId), applicationData)
    return response.data
  },

  // Save a job
  async saveJob(jobId: string): Promise<void> {
    await apiClient.post(API_ENDPOINTS.JOBS.SAVE(jobId))
  },

  // Unsave a job
  async unsaveJob(jobId: string): Promise<void> {
    await apiClient.delete(API_ENDPOINTS.JOBS.UNSAVE(jobId))
  },

  // Get saved jobs
  async getSavedJobs(): Promise<Job[]> {
    const response = await apiClient.get(API_ENDPOINTS.JOBS.SAVED)
    return response.data
  },

  // Search jobs
  async searchJobs(query: string, filters?: JobFilters): Promise<JobsResponse> {
    const params = { search: query, ...filters }
    const response = await apiClient.get(API_ENDPOINTS.JOBS.SEARCH, { params })
    return response.data
  }
}
