import { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { jobsApi } from "../api";
import { JobFilters, CreateJobData } from "../types";
import {
  setJobs,
  setLoading,
  setError,
  setFilters,
  addJob,
  updateJob,
  deleteJob,
} from "../jobsSlice";

export function useJobs() {
  const dispatch = useDispatch();
  const { jobs, loading, error, filters } = useSelector(
    (state: RootState) => state.jobs
  );

  const fetchJobs = useCallback(
    async (searchFilters?: JobFilters) => {
      try {
        dispatch(setLoading(true));
        dispatch(setError(null));

        const response = await jobsApi.getJobs(searchFilters);
        dispatch(setJobs(response));
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch jobs";
        dispatch(setError(errorMessage));
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  const createJob = useCallback(
    async (jobData: CreateJobData) => {
      try {
        dispatch(setLoading(true));
        dispatch(setError(null));

        const newJob = await jobsApi.createJob(jobData);
        dispatch(addJob(newJob));
        return { success: true, job: newJob };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create job";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  const updateJobById = useCallback(
    async (id: string, jobData: Partial<CreateJobData>) => {
      try {
        dispatch(setLoading(true));
        dispatch(setError(null));

        const updatedJob = await jobsApi.updateJob({ id, ...jobData });
        dispatch(updateJob(updatedJob));
        return { success: true, job: updatedJob };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update job";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  const deleteJobById = useCallback(
    async (id: string) => {
      try {
        dispatch(setLoading(true));
        dispatch(setError(null));

        await jobsApi.deleteJob(id);
        dispatch(deleteJob(id));
        return { success: true };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete job";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  const applyFilters = useCallback(
    (newFilters: JobFilters) => {
      dispatch(setFilters(newFilters));
      fetchJobs(newFilters);
    },
    [dispatch, fetchJobs]
  );

  // Auto-fetch jobs on mount
  useEffect(() => {
    if (jobs.length === 0) {
      fetchJobs(filters);
    }
  }, []);

  return {
    jobs,
    loading,
    error,
    filters,
    fetchJobs,
    createJob,
    updateJob: updateJobById,
    deleteJob: deleteJobById,
    applyFilters,
  };
}
