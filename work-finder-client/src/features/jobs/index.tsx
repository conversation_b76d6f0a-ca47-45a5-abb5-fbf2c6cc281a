import { JobList } from './components/JobList'
import { JobForm } from './components/JobForm'
import { JobFilter } from './components/JobFilter'
import { useJobs } from './hooks/useJobs'

export interface JobsFeatureProps {
  mode: 'list' | 'create' | 'edit'
  jobId?: string
  onSuccess?: () => void
}

export function JobsFeature({ mode, jobId, onSuccess }: JobsFeatureProps) {
  const { jobs, loading, error, fetchJobs, createJob, updateJob, deleteJob } = useJobs()

  const handleCreateJob = async (jobData: any) => {
    const result = await createJob(jobData)
    if (result.success && onSuccess) {
      onSuccess()
    }
  }

  const handleUpdateJob = async (jobData: any) => {
    if (!jobId) return
    const result = await updateJob({ id: jobId, ...jobData })
    if (result.success && onSuccess) {
      onSuccess()
    }
  }

  const handleDeleteJob = async (id: string) => {
    const result = await deleteJob(id)
    if (result.success && onSuccess) {
      onSuccess()
    }
  }

  if (mode === 'create') {
    return (
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Create New Job</h1>
        <JobForm onSubmit={handleCreateJob} loading={loading} />
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
      </div>
    )
  }

  if (mode === 'edit' && jobId) {
    return (
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Edit Job</h1>
        <JobForm 
          jobId={jobId}
          onSubmit={handleUpdateJob} 
          loading={loading} 
        />
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Jobs</h1>
        <JobFilter onFilter={fetchJobs} />
      </div>
      
      <JobList 
        jobs={jobs}
        loading={loading}
        onDelete={handleDeleteJob}
      />
      
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}
    </div>
  )
}

// Export individual components for flexibility
export { JobList, JobForm, JobFilter }
export { useJobs }
export * from './types'
