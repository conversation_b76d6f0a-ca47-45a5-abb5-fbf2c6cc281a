import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { Job, JobFilters, JobsState } from './types'
import { JobsResponse } from './api'

const initialState: JobsState = {
  jobs: [],
  loading: false,
  error: null,
  filters: {},
  currentJob: null
}

const jobsSlice = createSlice({
  name: 'jobs',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    setJobs: (state, action: PayloadAction<JobsResponse>) => {
      state.jobs = action.payload.jobs
      state.error = null
    },
    addJob: (state, action: PayloadAction<Job>) => {
      state.jobs.unshift(action.payload)
    },
    updateJob: (state, action: PayloadAction<Job>) => {
      const index = state.jobs.findIndex(job => job.id === action.payload.id)
      if (index !== -1) {
        state.jobs[index] = action.payload
      }
    },
    deleteJob: (state, action: PayloadAction<string>) => {
      state.jobs = state.jobs.filter(job => job.id !== action.payload)
    },
    setCurrentJob: (state, action: PayloadAction<Job | null>) => {
      state.currentJob = action.payload
    },
    setFilters: (state, action: PayloadAction<JobFilters>) => {
      state.filters = action.payload
    },
    clearFilters: (state) => {
      state.filters = {}
    },
    clearError: (state) => {
      state.error = null
    },
    clearJobs: (state) => {
      state.jobs = []
    }
  }
})

export const {
  setLoading,
  setError,
  setJobs,
  addJob,
  updateJob,
  deleteJob,
  setCurrentJob,
  setFilters,
  clearFilters,
  clearError,
  clearJobs
} = jobsSlice.actions

export default jobsSlice.reducer
