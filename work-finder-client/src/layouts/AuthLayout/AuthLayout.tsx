import { ReactNode } from 'react'
import { AuthBranding, AuthFooter } from './components'

interface AuthLayoutProps {
  children: ReactNode
  title?: string
  subtitle?: string
  maxWidth?: 'sm' | 'md' | 'lg'
  showFooter?: boolean
  className?: string
}

export function AuthLayout({ 
  children, 
  title,
  subtitle,
  maxWidth = 'md',
  showFooter = true,
  className = ""
}: AuthLayoutProps) {
  const maxWidthClasses = {
    'sm': 'sm:max-w-sm',
    'md': 'sm:max-w-md', 
    'lg': 'sm:max-w-lg'
  }

  return (
    <div className={`min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8 ${className}`}>
      <div className={`sm:mx-auto sm:w-full ${maxWidthClasses[maxWidth]}`}>
        <AuthBranding title={title} subtitle={subtitle} />
      </div>

      <div className={`mt-8 sm:mx-auto sm:w-full ${maxWidthClasses[maxWidth]}`}>
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {children}
        </div>
      </div>

      {showFooter && <AuthFooter />}
    </div>
  )
}