interface AuthFooterProps {
  year?: number
  companyName?: string
  className?: string
}

export function AuthFooter({ 
  year = new Date().getFullYear(),
  companyName = "WorkFinder",
  className = ""
}: AuthFooterProps) {
  return (
    <div className={`mt-8 text-center ${className}`}>
      <p className="text-xs text-gray-500">
        © {year} {companyName}. All rights reserved.
      </p>
    </div>
  )
}