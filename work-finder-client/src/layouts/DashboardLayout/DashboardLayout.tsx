import { ReactNode, useState } from 'react'
import { Sidebar, TopBar } from './components'

interface NavigationItem {
  name: string
  href: string
  icon: string
  current?: boolean
}

interface DashboardLayoutProps {
  children: ReactNode
  navigation?: NavigationItem[]
  showSidebar?: boolean
  maxWidth?: 'full' | '7xl' | '6xl' | '5xl'
  className?: string
}

export function DashboardLayout({ 
  children, 
  navigation,
  showSidebar = true,
  maxWidth = '7xl',
  className = ""
}: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const maxWidthClasses = {
    'full': 'max-w-full',
    '7xl': 'max-w-7xl',
    '6xl': 'max-w-6xl', 
    '5xl': 'max-w-5xl'
  }

  return (
    <div className={`min-h-screen bg-gray-100 ${className}`}>
      {showSidebar && (
        <Sidebar 
          navigation={navigation}
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
        />
      )}

      <div className={showSidebar ? "lg:pl-64 flex flex-col flex-1" : "flex flex-col flex-1"}>
        <TopBar onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1">
          <div className="py-6">
            <div className={`mx-auto ${maxWidthClasses[maxWidth]} px-4 sm:px-6 lg:px-8`}>
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}