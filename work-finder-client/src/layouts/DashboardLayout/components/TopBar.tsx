import { useSelector } from 'react-redux'
import { RootState } from '@/store'

interface TopBarProps {
  onMenuClick?: () => void
  className?: string
}

export function TopBar({ onMenuClick, className = "" }: TopBarProps) {
  const { user } = useSelector((state: RootState) => state.auth)

  return (
    <div className={`sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200 ${className}`}>
      <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
        <button
          type="button"
          className="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
          onClick={onMenuClick}
        >
          <span className="sr-only">Open sidebar</span>
          ☰
        </button>
        
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-700">Welcome, {user?.name}</span>
          <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
            {user?.name?.charAt(0).toUpperCase()}
          </div>
        </div>
      </div>
    </div>
  )
}