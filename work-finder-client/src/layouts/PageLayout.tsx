import { ReactNode } from 'react'
import { <PERSON><PERSON> } from '@/components/templates/Header'
import { Footer } from '@/components/templates/Footer'

interface PageLayoutProps {
  children: ReactNode
  showHeader?: boolean
  showFooter?: boolean
  className?: string
}

export function PageLayout({ 
  children, 
  showHeader = true, 
  showFooter = true,
  className = '' 
}: PageLayoutProps) {
  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      {showHeader && <Header />}
      
      <main className="flex-1">
        {children}
      </main>
      
      {showFooter && <Footer />}
    </div>
  )
}
