import { ReactNode } from 'react'
import { PublicHeader, PublicFooter } from './components'

interface NavigationItem {
  label: string
  href: string
  hasDropdown?: boolean
}

interface FooterLinkGroup {
  title: string
  links: Array<{
    label: string
    href: string
    isExternal?: boolean
  }>
}

interface PublicLayoutProps {
  children: ReactNode
  showHeader?: boolean
  showFooter?: boolean
  showUploadCV?: boolean
  navigation?: NavigationItem[]
  footerLinkGroups?: FooterLinkGroup[]
  showSocialLinks?: boolean
  className?: string
}

export function PublicLayout({ 
  children, 
  showHeader = true, 
  showFooter = true,
  showUploadCV = true,
  navigation,
  footerLinkGroups,
  showSocialLinks = true,
  className = "" 
}: PublicLayoutProps) {
  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      {showHeader && (
        <PublicHeader 
          navigation={navigation}
          showUploadCV={showUploadCV}
        />
      )}
      
      <main className="flex-1">
        {children}
      </main>
      
      {showFooter && (
        <PublicFooter 
          linkGroups={footerLinkGroups}
          showSocialLinks={showSocialLinks}
        />
      )}
    </div>
  )
}