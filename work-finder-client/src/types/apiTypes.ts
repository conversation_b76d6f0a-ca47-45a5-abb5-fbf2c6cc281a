// Global API response types used across all features

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
  errors?: string[]
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface ApiError {
  message: string
  code?: string
  field?: string
  details?: Record<string, any>
}

export interface ValidationError {
  field: string
  message: string
  code: string
}

export interface ApiErrorResponse {
  success: false
  message: string
  errors?: ValidationError[]
  code?: string
}

// Search and suggestion types
export interface SearchSuggestion {
  id: string
  type: 'job' | 'company' | 'location' | 'skill'
  text: string
  count?: number
}

export interface SearchResult<T> {
  items: T[]
  total: number
  query: string
  suggestions?: SearchSuggestion[]
  filters?: Record<string, any>
}

// Notification types (used across features)
export interface NotificationSettings {
  email: boolean
  push: boolean
  sms: boolean
}

export interface Notification {
  id: string
  userId: string
  type: 'job_alert' | 'application_update' | 'interview_reminder' | 'company_update' | 'system'
  title: string
  message: string
  data?: Record<string, any>
  isRead: boolean
  createdAt: string
  expiresAt?: string
}
