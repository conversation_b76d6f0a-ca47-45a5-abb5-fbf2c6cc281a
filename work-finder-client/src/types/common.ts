// Common utility types used across multiple features

export type Status = 'idle' | 'loading' | 'success' | 'error'

export type SortOrder = 'asc' | 'desc'

export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
}

export interface BaseFilters {
  search?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: SortOrder
}

export interface Location {
  city: string
  state: string
  country: string
}

export interface DateRange {
  from?: string
  to?: string
}

export interface FileUpload {
  id: string
  name: string
  url: string
  size: number
  type: string
  uploadedAt: string
}

// UI Component types
export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export interface TabItem {
  id: string
  label: string
  content: React.ReactNode
  disabled?: boolean
}

export interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}
